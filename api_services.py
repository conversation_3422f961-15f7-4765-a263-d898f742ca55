# -*- encoding: utf-8 -*-
import json
import logging
logger = logging.getLogger(__name__)
import requests

# MayaClient imports
try:
    from rserving_client.rserving_client import (
        ArksConfig,
        ArksRequest,
        MayaClient
    )
    MAYA_CLIENT_AVAILABLE = True
except ImportError:
    logger.warning("Maya<PERSON>lient not available, falling back to POST requests")
    MAYA_CLIENT_AVAILABLE = False



class GenericAPICaller:
    """
    通用的服务调用类，支持 MayaClient 和 POST 两种模式。

    初始化时确定调用模式以及各服务的参数；
    - scene_name: Maya 的场景名（仅 maya 模式需要）
    - post_url: POST 请求地址（仅 post 模式需要）
    - input_key: 输入 tensor 的 key
    - output_key: 输出字段的 key
    - headers: POST 请求 headers（可选）
    - output_json_parse: 是否尝试将输出解析为 JSON（可选）
    - output_json_nested_key: 若解析为 JSON 后需要从中提取某个嵌套字段，则指定该 key（可选）
    """

    def __init__(
        self,
        input_key: str,
        output_key: str,
        scene_name=None,
        post_url=None,
        headers=None,
        output_json_parse: bool = False,
        output_json_nested_key=None,
    ) -> None:
        self.input_key = input_key
        self.output_key = output_key
        self.scene_name = scene_name
        self.post_url = post_url
        self.headers = headers or {
            "Content-Type": "application/json;charset=utf-8",
            "MPS-app-name": "your-app-name",
            "MPS-http-version": "1.0",
            "MPS-trace-id": "your-trace-id",
        }
        self.output_json_parse = output_json_parse
        self.output_json_nested_key = output_json_nested_key

    def _build_post_body(self, data: dict) -> dict:
        # 按照目前服务约定，直接使用传入的 data，最简模板
        return {
            "features": {},
            "tensorFeatures": {
                self.input_key: {
                    "shapes": [1],
                    "stringValues": [json.dumps(data)],
                }
            },
        }

    def _postprocess_output(self, value):
        # 根据配置决定是否解析为 JSON，并且是否抽取嵌套字段
        if self.output_json_parse:
            try:
                parsed = json.loads(value) if isinstance(value, str) else value
            except Exception as e:
                logger.error(f"Failed to parse output JSON: {e}")
                return {"error": f"Failed to parse output JSON: {e}"}

            if self.output_json_nested_key:
                if isinstance(parsed, dict) and self.output_json_nested_key in parsed:
                    return parsed[self.output_json_nested_key]
                else:
                    return {"error": f"No '{self.output_json_nested_key}' field in response data"}
            return parsed
        # 不解析，原样返回
        return value

    def call(self, data: dict, timeout: int = 30000):
        """
        统一的调用入口。异常统一处理，返回 dict 或错误信息。
        timeout: 毫秒
        """
        mode = data.get('mode', 'maya')
        try:
            if mode == "maya":
                logger.info("Using MayaClient to call service")
                client = MayaClient.get_instance()
                request = ArksRequest()
                request.request_timeout = timeout
                request.read_timeout = timeout
                request.connect_timeout = timeout
                request.scene_name = self.scene_name or ""
                request.chain_name = "v1"
                request.session_id = "traceId"
                request.set_lbconfig({"arks.antvip.arrow.cross.city": "true"})
                request.set_item_num(1)

                item = request.add_item("item-1")
                item.add_tensor_feature(self.input_key, [1], [json.dumps(data)])

                res = client.call(request)
                if res.success:
                    attrs = res.items[0].attributes
                    if self.output_key not in attrs:
                        return {"error": f"No {self.output_key} in response"}
                    return self._postprocess_output(attrs[self.output_key])
                else:
                    logger.error(f"MayaClient call failed: {res.error_msg}")
                    return {"error": res.error_msg}

            elif mode == "post":
                logger.info("Using POST to call service")
                body = self._build_post_body(data)
                response = requests.post(url=self.post_url, json=body, headers=self.headers)
                res = response.json()

                if res.get("success") and "resultMap" in res and self.output_key in res["resultMap"]:
                    return self._postprocess_output(res["resultMap"][self.output_key])
                else:
                    logger.error(f"POST call failed: {res}")
                    return {"error": res.get("errorMessage", "Unknown error")}

            else:
                return {"error": f"Unsupported mode: {mode}"}

        except Exception as e:
            logger.error(f"Service call exception: {e}")
            return {"error": str(e)}

def get_subject_analysis(data):
    """
    获取主体分析结果（使用通用调用器）
    """
    caller = GenericAPICaller(
        scene_name="main_recognition_vlm",
        post_url="https://paiplusinference.alipay.com/inference/ccb90aaa457e5bb2_main_recognition_vlm/v1",
        input_key="input_args",
        output_key="output_args",
    )
    timeout = data.get("timeout", 30000)
    return caller.call(data, timeout=timeout)


def enhance_image_quality(data):
    """
    调用画质增强模块（使用通用调用器）
    """
    caller = GenericAPICaller(
        scene_name="main_deeplpf",
        post_url="https://paiplusinference.alipay.com/inference/c679eb929e2cdd91_main_deeplpf/v1",
        input_key="input",
        output_key="output",
        # 该服务的输出是字符串化 JSON，且需要从其中的 'output' 字段中拿到最终结果
        output_json_parse=True,
        # output_json_nested_key="output",
    )
    timeout = data.get("timeout", 30000)
    return caller.call(data, timeout=timeout)
