#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
临时脚本：过滤之前输出的表格，只保留增强成功的行
只保留aftsId/aftsid字段不为空的行
"""

import pandas as pd
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("FilterResults")


def filter_successful_results(input_file, output_file):
    """过滤成功的结果"""
    try:
        # 读取CSV文件
        logger.info(f"Reading input file: {input_file}")
        df = pd.read_csv(input_file)
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # 显示列名
        logger.info(f"Columns: {list(df.columns)}")
        
        # 检查可能的aftsId列名（兼容不同的命名）
        afts_column = None
        possible_columns = ['aftsId', 'aftsid', 'afts_id']
        
        for col in possible_columns:
            if col in df.columns:
                afts_column = col
                break
        
        if afts_column is None:
            logger.error(f"未找到aftsId相关列，可用列: {list(df.columns)}")
            return
        
        logger.info(f"使用列名: {afts_column}")
        
        # 过滤前的统计
        total_rows = len(df)
        empty_afts = df[afts_column].isna() | (df[afts_column] == '') | (df[afts_column] == '0')
        empty_count = empty_afts.sum()
        success_count = total_rows - empty_count
        
        logger.info(f"过滤前统计:")
        logger.info(f"  总行数: {total_rows}")
        logger.info(f"  空aftsId行数: {empty_count}")
        logger.info(f"  成功行数: {success_count}")
        
        # 过滤：只保留aftsId不为空的行
        filtered_df = df[~empty_afts].copy()
        
        logger.info(f"过滤后行数: {len(filtered_df)}")
        
        # 保存结果
        filtered_df.to_csv(output_file, index=False)
        logger.info(f"过滤结果保存到: {output_file}")
        
        # 显示一些样本数据
        if len(filtered_df) > 0:
            logger.info("前5行样本数据:")
            print(filtered_df.head().to_string())
        
        return {
            'total_rows': total_rows,
            'empty_count': empty_count,
            'success_count': success_count,
            'filtered_count': len(filtered_df)
        }
        
    except Exception as e:
        logger.error(f"处理失败: {e}")
        return None


def main():
    """主函数"""
    # 配置文件路径
    input_files = [
        "tests/3_top2k_with_results_enhanced.csv",
        # 可以添加更多文件
    ]
    
    for input_file in input_files:
        try:
            # 生成输出文件名
            if input_file.endswith('.csv'):
                output_file = input_file.replace('.csv', '_filtered.csv')
            else:
                output_file = input_file + '_filtered.csv'
            
            logger.info(f"\n{'='*50}")
            logger.info(f"处理文件: {input_file}")
            logger.info(f"输出文件: {output_file}")
            logger.info(f"{'='*50}")
            
            # 过滤文件
            result = filter_successful_results(input_file, output_file)
            
            if result:
                print(f"\n处理完成:")
                print(f"  输入文件: {input_file}")
                print(f"  输出文件: {output_file}")
                print(f"  原始行数: {result['total_rows']}")
                print(f"  成功行数: {result['filtered_count']}")
                print(f"  过滤掉: {result['empty_count']} 行")
                print(f"  保留率: {result['filtered_count']/result['total_rows']*100:.1f}%")
            
        except FileNotFoundError:
            logger.warning(f"文件不存在，跳过: {input_file}")
        except Exception as e:
            logger.error(f"处理文件失败 {input_file}: {e}")


if __name__ == "__main__":
    main()
