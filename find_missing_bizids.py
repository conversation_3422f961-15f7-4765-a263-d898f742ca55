#!/usr/bin/env python3
"""
脚本用于找出在 filtered 文件中存在但在 original 文件中不存在的 bizId，
并将这些缺失的行保存到一个新的 CSV 文件中。
"""

import pandas as pd
import os

def find_missing_bizids():
    # 文件路径
    filtered_file = 'tests/3_top2k_with_results_enhanced_filtered.csv'
    original_file = 'tests/3_top2k_with_results_enhanced_original.csv'
    output_file = 'tests/missing_bizids.csv'
    
    # 检查文件是否存在
    if not os.path.exists(filtered_file):
        print(f"错误: 文件 {filtered_file} 不存在")
        return
    
    if not os.path.exists(original_file):
        print(f"错误: 文件 {original_file} 不存在")
        return
    
    try:
        # 读取两个CSV文件
        print("正在读取 filtered 文件...")
        df_filtered = pd.read_csv(filtered_file)
        print(f"filtered 文件包含 {len(df_filtered)} 行数据")
        
        print("正在读取 original 文件...")
        df_original = pd.read_csv(original_file)
        print(f"original 文件包含 {len(df_original)} 行数据")
        
        # 获取两个文件中的 bizId 集合
        bizids_filtered = set(df_filtered['bizId'])
        bizids_original = set(df_original['bizId'])
        
        print(f"filtered 文件中有 {len(bizids_filtered)} 个唯一的 bizId")
        print(f"original 文件中有 {len(bizids_original)} 个唯一的 bizId")
        
        # 找出在 filtered 中存在但在 original 中不存在的 bizId
        missing_bizids = bizids_filtered - bizids_original
        
        print(f"找到 {len(missing_bizids)} 个在 filtered 中存在但在 original 中不存在的 bizId")
        
        if len(missing_bizids) == 0:
            print("没有找到缺失的 bizId")
            return
        
        # 从 filtered 文件中筛选出这些缺失的行
        missing_rows = df_filtered[df_filtered['bizId'].isin(missing_bizids)]
        
        # 保存到新的CSV文件
        missing_rows.to_csv(output_file, index=False)
        print(f"已将 {len(missing_rows)} 行缺失数据保存到 {output_file}")
        
        # 显示一些统计信息
        print("\n缺失的 bizId 列表:")
        for bizid in sorted(missing_bizids):
            print(f"  {bizid}")
        
        print(f"\n输出文件: {output_file}")
        print("完成!")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    find_missing_bizids()
